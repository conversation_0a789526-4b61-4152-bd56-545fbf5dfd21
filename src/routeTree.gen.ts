/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as TakeoutRouteImport } from './routes/takeout'
import { Route as ReservationRouteImport } from './routes/reservation'
import { Route as MenuRouteImport } from './routes/menu'
import { Route as IndexRouteImport } from './routes/index'

const TakeoutRoute = TakeoutRouteImport.update({
  id: '/takeout',
  path: '/takeout',
  getParentRoute: () => rootRouteImport,
} as any)
const ReservationRoute = ReservationRouteImport.update({
  id: '/reservation',
  path: '/reservation',
  getParentRoute: () => rootRouteImport,
} as any)
const MenuRoute = MenuRouteImport.update({
  id: '/menu',
  path: '/menu',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/menu': typeof MenuRoute
  '/reservation': typeof ReservationRoute
  '/takeout': typeof TakeoutRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/menu': typeof MenuRoute
  '/reservation': typeof ReservationRoute
  '/takeout': typeof TakeoutRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/menu': typeof MenuRoute
  '/reservation': typeof ReservationRoute
  '/takeout': typeof TakeoutRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/' | '/menu' | '/reservation' | '/takeout'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '/menu' | '/reservation' | '/takeout'
  id: '__root__' | '/' | '/menu' | '/reservation' | '/takeout'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  MenuRoute: typeof MenuRoute
  ReservationRoute: typeof ReservationRoute
  TakeoutRoute: typeof TakeoutRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/takeout': {
      id: '/takeout'
      path: '/takeout'
      fullPath: '/takeout'
      preLoaderRoute: typeof TakeoutRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/reservation': {
      id: '/reservation'
      path: '/reservation'
      fullPath: '/reservation'
      preLoaderRoute: typeof ReservationRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/menu': {
      id: '/menu'
      path: '/menu'
      fullPath: '/menu'
      preLoaderRoute: typeof MenuRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  MenuRoute: MenuRoute,
  ReservationRoute: ReservationRoute,
  TakeoutRoute: TakeoutRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

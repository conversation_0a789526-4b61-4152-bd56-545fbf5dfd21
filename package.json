{"name": "nepali-dining", "private": true, "type": "module", "scripts": {"dev": "vite dev --port 3000", "start": "node .output/server/index.mjs", "build": "vite build", "serve": "vite preview", "test": "vitest run", "lint": "eslint", "format": "prettier", "check": "prettier --write . && eslint --fix"}, "dependencies": {"@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "@tailwindcss/vite": "^4.0.6", "@tanstack/react-devtools": "^0.2.2", "@tanstack/react-router": "^1.130.2", "@tanstack/react-router-devtools": "^1.131.5", "@tanstack/react-router-ssr-query": "^1.131.7", "@tanstack/react-start": "^1.131.7", "@tanstack/router-plugin": "^1.121.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.6.0", "lucide-react": "^0.476.0", "motion": "^12.23.12", "react": "^19.0.0", "react-dom": "^19.0.0", "rough-notation": "^0.5.1", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.0.6", "tw-animate-css": "^1.3.6", "vaul": "^1.1.2", "vite-tsconfig-paths": "^5.1.4"}, "devDependencies": {"@tanstack/eslint-config": "^0.3.0", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.2.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "jsdom": "^26.0.0", "prettier": "^3.5.3", "typescript": "^5.7.2", "vite": "^6.3.5", "vitest": "^3.0.5", "web-vitals": "^4.2.4"}}